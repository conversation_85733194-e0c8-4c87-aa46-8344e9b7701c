<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Polylines</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">Polylines</div>
        <script type="module">
            import { Renderer, Transform, Vec3, Color, Polyline } from '../src/index.js';

            const vertex = /* glsl */ `
                precision highp float;

                attribute vec3 position;
                attribute vec3 next;
                attribute vec3 prev;
                attribute vec2 uv;
                attribute float side;

                uniform vec2 uResolution;
                uniform float uDPR;
                uniform float uThickness;

                vec4 getPosition() {
                    // 将当前顶点位置转换为齐次坐标（添加w分量为1）
                    vec4 current = vec4(position, 1);

                    // 计算屏幕宽高比，用于校正不同屏幕比例下的变形
                    vec2 aspect = vec2(uResolution.x / uResolution.y, 1);
                    // 将下一个顶点位置应用宽高比校正，转换到屏幕空间
                    vec2 nextScreen = next.xy * aspect;
                    // 将上一个顶点位置应用宽高比校正，转换到屏幕空间
                    vec2 prevScreen = prev.xy * aspect;

                    // 计算线段的切线方向（从前一个点到下一个点的方向向量）
                    vec2 tangent = normalize(nextScreen - prevScreen);

                    // 将切线向量逆时针旋转90度得到法线向量（垂直于线段的方向）
                    // 旋转矩阵：[0 -1; 1 0] 应用到 [tangent.x, tangent.y]
                    vec2 normal = vec2(-tangent.y, tangent.x);
                    // 将法线向量除以宽高比，还原到原始坐标空间
                    normal /= aspect;

                    // 使用uv.y坐标实现线条的锥形效果：中间粗，两端细
                    // abs(uv.y - 0.5) * 2.0 将uv.y从[0,1]映射到[0,1]，中心为0，两端为1
                    // pow(..., 2.0) 使过渡更加平滑
                    // mix(1.0, 0.1, ...) 在1.0（中间）和0.1（两端）之间插值
                    normal *= mix(1.0, 0.1, pow(abs(uv.y - 0.5) * 2.0, 2.0) );

                    // 当相邻两点距离过近时，缩小线条宽度以避免渲染伪影
                    float dist = length(nextScreen - prevScreen);
                    // smoothstep(0.0, 0.02, dist) 当距离小于0.02时平滑过渡到0
                    normal *= smoothstep(0.0, 0.02, dist);

                    // 计算像素宽度比例，用于将线条宽度转换为像素单位
                    // uDPR是设备像素比，uResolution.y是屏幕高度
                    float pixelWidthRatio = 1.0 / (uResolution.y / uDPR);
                    // 计算当前顶点的像素宽度（考虑透视投影的w分量）
                    float pixelWidth = current.w * pixelWidthRatio;
                    // 将法线向量缩放到最终的线条宽度
                    normal *= pixelWidth * uThickness;
                    // 根据side值（-1或1）将顶点沿法线方向偏移，形成线条的两侧边缘
                    current.xy -= normal * side;

                    // 返回计算后的顶点位置
                    return current;
                }

                void main() {
                    gl_Position = getPosition();
                }
            `;

            {
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                gl.clearColor(0.9, 0.9, 0.9, 1);

                const scene = new Transform();

                const lines = [];

                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);

                    // We call resize on the polylines to update their resolution uniforms
                    lines.forEach((line) => line.polyline.resize());
                }
                window.addEventListener('resize', resize, false);

                // Just a helper function to make the code neater
                function random(a, b) {
                    const alpha = Math.random();
                    return a * (1.0 - alpha) + b * alpha;
                }

                // If you're interested in learning about drawing lines with geometry,
                // go through this detailed article by Matt DesLauriers
                // https://mattdesl.svbtle.com/drawing-lines-is-hard
                // It's an excellent breakdown of the approaches and their pitfalls.

                // In this example, we're making screen-space polylines. Basically it
                // involves creating a geometry of vertices along a path - with two vertices
                // at each point. Then in the vertex shader, we push each pair apart to
                // give the line some width.

                // We're going to make a number of different coloured lines for fun.
                ['#e09f7d', '#ef5d60', '#ec4067', '#a01a7d', '#311847'].forEach((color, i) => {
                    // Store a few values for each lines' spring movement
                    const line = {
                        spring: random(0.02, 0.1),
                        friction: random(0.7, 0.95),
                        mouseVelocity: new Vec3(),
                        mouseOffset: new Vec3(random(-1, 1) * 0.02),
                    };

                    // Create an array of Vec3s (eg [[0, 0, 0], ...])
                    // Note: Only pass in one for each point on the line - the class will handle
                    // the doubling of vertices for the polyline effect.
                    const count = 20;
                    const points = (line.points = []);
                    for (let i = 0; i < count; i++) points.push(new Vec3());

                    // Pass in the points, and any custom elements - for example here we've made
                    // custom shaders, and accompanying uniforms.
                    line.polyline = new Polyline(gl, {
                        points,
                        vertex,
                        uniforms: {
                            uColor: { value: new Color(color) },
                            uThickness: { value: random(20, 50) },
                        },
                    });

                    line.polyline.mesh.setParent(scene);

                    lines.push(line);
                });

                // Call initial resize after creating the polylines
                resize();

                // Add handlers to get mouse position
                const mouse = new Vec3();
                if ('ontouchstart' in window) {
                    window.addEventListener('touchstart', updateMouse, false);
                    window.addEventListener('touchmove', updateMouse, false);
                } else {
                    window.addEventListener('mousemove', updateMouse, false);
                }

                function updateMouse(e) {
                    if (e.changedTouches && e.changedTouches.length) {
                        e.x = e.changedTouches[0].pageX;
                        e.y = e.changedTouches[0].pageY;
                    }
                    if (e.x === undefined) {
                        e.x = e.pageX;
                        e.y = e.pageY;
                    }

                    // Get mouse value in -1 to 1 range, with y flipped
                    mouse.set((e.x / gl.renderer.width) * 2 - 1, (e.y / gl.renderer.height) * -2 + 1, 0);
                }

                const tmp = new Vec3();

                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    lines.forEach((line) => {
                        // Update polyline input points
                        for (let i = line.points.length - 1; i >= 0; i--) {
                            if (!i) {
                                // For the first point, spring ease it to the mouse position
                                tmp.copy(mouse).add(line.mouseOffset).sub(line.points[i]).multiply(line.spring);
                                line.mouseVelocity.add(tmp).multiply(line.friction);
                                line.points[i].add(line.mouseVelocity);
                            } else {
                                // The rest of the points ease to the point in front of them, making a line
                                line.points[i].lerp(line.points[i - 1], 0.9);
                            }
                        }
                        line.polyline.updateGeometry();
                    });

                    renderer.render({ scene });
                }
            }
        </script>
    </body>
</html>
