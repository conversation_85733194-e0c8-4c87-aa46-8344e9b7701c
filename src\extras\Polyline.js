import { Geometry } from '../core/Geometry.js';
import { Program } from '../core/Program.js';
import { Mesh } from '../core/Mesh.js';
import { Vec2 } from '../math/Vec2.js';
import { Vec3 } from '../math/Vec3.js';
import { Color } from '../math/Color.js';

// 预先创建临时向量以避免重复创建对象
const tmp = /* @__PURE__ */ new Vec3();

/**
 * 多段线类
 * 用于创建具有可变宽度和平滑连接的3D线条
 */
export class Polyline {
    /**
     * 创建一个多段线实例
     * @param {WebGLRenderingContext} gl - WebGL上下文
     * @param {Object} options - 配置选项
     * @param {Array<Vec3>} options.points - 线条的点数组
     * @param {String} [options.vertex=defaultVertex] - 顶点着色器代码
     * @param {String} [options.fragment=defaultFragment] - 片段着色器代码
     * @param {Object} [options.uniforms={}] - 着色器统一变量
     * @param {Object} [options.attributes={}] - 自定义几何体属性
     */
    constructor(
        gl,
        {
            points, // Vec3数组
            vertex = defaultVertex,
            fragment = defaultFragment,
            uniforms = {},
            attributes = {}, // 用于传入自定义属性
        }
    ) {
        this.gl = gl;
        this.points = points;
        this.count = points.length;

        // 创建缓冲区
        // 每个点需要两个顶点（线的两侧）
        this.position = new Float32Array(this.count * 3 * 2); // 位置
        this.prev = new Float32Array(this.count * 3 * 2); // 前一个点
        this.next = new Float32Array(this.count * 3 * 2); // 下一个点
        const side = new Float32Array(this.count * 1 * 2); // 线的哪一侧（-1或1）
        const uv = new Float32Array(this.count * 2 * 2); // 纹理坐标
        const index = new Uint16Array((this.count - 1) * 3 * 2); // 索引（每段线由2个三角形组成）

        // 设置静态缓冲区
        for (let i = 0; i < this.count; i++) {
            // 设置每个点的两侧
            side.set([-1, 1], i * 2);

            // 计算纹理坐标v（沿线的位置）
            const v = i / (this.count - 1);
            // 设置纹理坐标 [u, v] 对于线的两侧：[0,v]和[1,v]
            uv.set([0, v, 1, v], i * 4);

            // 跳过最后一个点的索引设置
            if (i === this.count - 1) continue;

            // 创建两个三角形连接当前点和下一个点的两侧
            const ind = i * 2;
            // 三角形1：当前点左侧、当前点右侧、下一点左侧
            index.set([ind + 0, ind + 1, ind + 2], (ind + 0) * 3);
            // 三角形2：下一点左侧、当前点右侧、下一点右侧
            index.set([ind + 2, ind + 1, ind + 3], (ind + 1) * 3);
        }

        // 创建几何体
        const geometry = (this.geometry = new Geometry(
            gl,
            Object.assign(attributes, {
                position: { size: 3, data: this.position }, // 顶点位置
                prev: { size: 3, data: this.prev }, // 前一个点位置
                next: { size: 3, data: this.next }, // 下一个点位置
                side: { size: 1, data: side }, // 线的哪一侧
                uv: { size: 2, data: uv }, // 纹理坐标
                index: { size: 1, data: index }, // 索引
            })
        ));

        // 填充动态缓冲区
        this.updateGeometry();

        // 设置默认的统一变量（如果未提供）
        if (!uniforms.uResolution) this.resolution = uniforms.uResolution = { value: new Vec2() }; // 画布分辨率
        if (!uniforms.uDPR) this.dpr = uniforms.uDPR = { value: 1 }; // 设备像素比
        if (!uniforms.uThickness) this.thickness = uniforms.uThickness = { value: 1 }; // 线条粗细
        if (!uniforms.uColor) this.color = uniforms.uColor = { value: new Color('#000') }; // 线条颜色
        if (!uniforms.uMiter) this.miter = uniforms.uMiter = { value: 1 }; // 斜接限制

        // 设置尺寸相关统一变量的值
        this.resize();

        // 创建着色器程序
        const program = (this.program = new Program(gl, {
            vertex,
            fragment,
            uniforms,
        }));

        // 创建网格
        this.mesh = new Mesh(gl, { geometry, program });
    }

    /**
     * 更新几何体数据
     * 根据当前点数组更新位置、前一个点和下一个点的缓冲区
     */
    updateGeometry() {
        this.points.forEach((p, i) => {
            // 设置当前点的位置（线的两侧都使用相同的位置）
            p.toArray(this.position, i * 3 * 2);
            p.toArray(this.position, i * 3 * 2 + 3);

            if (!i) {
                // 如果是第一个点，根据到第二个点的距离计算前一个点
                // 通过将第一个点向第二个点的反方向延伸来模拟前一个点
                tmp.copy(p)
                    .sub(this.points[i + 1]) // 从第一个点减去第二个点
                    .add(p); // 再加上第一个点，得到延伸点
                tmp.toArray(this.prev, i * 3 * 2);
                tmp.toArray(this.prev, i * 3 * 2 + 3);
            } else {
                // 对于非第一个点，当前点是前一个点的下一个点
                p.toArray(this.next, (i - 1) * 3 * 2);
                p.toArray(this.next, (i - 1) * 3 * 2 + 3);
            }

            if (i === this.points.length - 1) {
                // 如果是最后一个点，根据到倒数第二个点的距离计算下一个点
                // 通过将最后一个点向倒数第二个点的反方向延伸来模拟下一个点
                tmp.copy(p)
                    .sub(this.points[i - 1]) // 从最后一个点减去倒数第二个点
                    .add(p); // 再加上最后一个点，得到延伸点
                tmp.toArray(this.next, i * 3 * 2);
                tmp.toArray(this.next, i * 3 * 2 + 3);
            } else {
                // 对于非最后一个点，当前点是下一个点的前一个点
                p.toArray(this.prev, (i + 1) * 3 * 2);
                p.toArray(this.prev, (i + 1) * 3 * 2 + 3);
            }
        });

        // 标记缓冲区需要更新
        this.geometry.attributes.position.needsUpdate = true;
        this.geometry.attributes.prev.needsUpdate = true;
        this.geometry.attributes.next.needsUpdate = true;
    }

    /**
     * 调整线条大小
     * 只有在不手动处理分辨率统一变量时才需要调用
     */
    resize() {
        // 如果未覆盖，则更新自动统一变量
        if (this.resolution) this.resolution.value.set(this.gl.canvas.width, this.gl.canvas.height);
        if (this.dpr) this.dpr.value = this.gl.renderer.dpr;
    }
}

/**
 * 默认顶点着色器
 * 用于渲染具有可变宽度和平滑连接的线条
 */
const defaultVertex = /* glsl */ `
    precision highp float;

    // 顶点属性
    attribute vec3 position;  // 当前点位置
    attribute vec3 next;      // 下一个点位置
    attribute vec3 prev;      // 前一个点位置
    attribute vec2 uv;        // 纹理坐标
    attribute float side;     // 线的哪一侧（-1或1）

    // 统一变量
    uniform mat4 modelViewMatrix;     // 模型视图矩阵
    uniform mat4 projectionMatrix;    // 投影矩阵
    uniform vec2 uResolution;         // 画布分辨率
    uniform float uDPR;               // 设备像素比
    uniform float uThickness;         // 线条粗细
    uniform float uMiter;             // 斜接限制（控制尖角的锐利程度）

    // 传递给片段着色器的变量
    varying vec2 vUv;

    /**
     * 计算顶点在屏幕空间中的位置
     * 考虑线条宽度和连接处的平滑处理
     */
    vec4 getPosition() {
        // 将当前点、下一个点和前一个点转换到裁剪空间
        mat4 mvp = projectionMatrix * modelViewMatrix;
        vec4 current = mvp * vec4(position, 1);
        vec4 nextPos = mvp * vec4(next, 1);
        vec4 prevPos = mvp * vec4(prev, 1);

        // 考虑屏幕宽高比，将点转换到屏幕空间
        vec2 aspect = vec2(uResolution.x / uResolution.y, 1);
        vec2 currentScreen = current.xy / current.w * aspect;
        vec2 nextScreen = nextPos.xy / nextPos.w * aspect;
        vec2 prevScreen = prevPos.xy / prevPos.w * aspect;

        // 计算前后方向向量
        vec2 dir1 = normalize(currentScreen - prevScreen);
        vec2 dir2 = normalize(nextScreen - currentScreen);
        // 计算平均方向向量
        vec2 dir = normalize(dir1 + dir2);

        // 计算法线向量（垂直于方向向量）
        vec2 normal = vec2(-dir.y, dir.x);
        // 应用斜接限制，防止在尖角处法线过长
        normal /= mix(1.0, max(0.3, dot(normal, vec2(-dir1.y, dir1.x))), uMiter);
        // 恢复宽高比
        normal /= aspect;

        // 计算像素宽度比例
        float pixelWidthRatio = 1.0 / (uResolution.y / uDPR);
        float pixelWidth = current.w * pixelWidthRatio;
        // 应用线条粗细
        normal *= pixelWidth * uThickness;
        // 根据side值偏移顶点位置
        current.xy -= normal * side;

        return current;
    }

    void main() {
        // 传递纹理坐标给片段着色器
        vUv = uv;
        // 计算最终顶点位置
        gl_Position = getPosition();
    }
`;

/**
 * 默认片段着色器
 * 用于渲染线条的颜色
 */
const defaultFragment = /* glsl */ `
    precision highp float;

    // 统一变量
    uniform vec3 uColor;  // 线条颜色

    // 从顶点着色器接收的变量
    varying vec2 vUv;

    void main() {
        // 设置片段颜色
        gl_FragColor.rgb = uColor;
        gl_FragColor.a = 1.0;  // 不透明
    }
`;
